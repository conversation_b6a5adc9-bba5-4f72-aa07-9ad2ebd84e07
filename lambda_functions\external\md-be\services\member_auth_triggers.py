import logging
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from models.member import <PERSON><PERSON><PERSON><PERSON>, CoAuth0User
from utils.response_utils import created_response, success_response
from services.auth0_identity_service import auth0_identity_service
from models.member_verification import MemberVerification

logger = logging.getLogger(__name__)

def post_registration_trigger(request, db: Session):
    try:
        email = request.email
        user_id = request.user_id
        identities = request.identities or []

        # Validate input parameters
        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Check if this exact user_id already exists
        existing_member_by_id = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if existing_member_by_id:
            logger.info(f"User {user_id} already exists in database")
            return created_response(
                "User already exists",
                {}
            )

        # Check if a user with the same email already exists (different user_id)
        existing_member_by_email = db.query(CoAuth0User).filter_by(email=email).first()

        if existing_member_by_email and existing_member_by_email.userId != user_id:
            logger.info(f"Found existing user with email {email}, linking identities synchronously")

            try:
                auth0_identity_service.process_comprehensive_identity_linking(
                    email, user_id, identities, request
                )

                existing_member_by_email.userId = user_id
                db.commit()

                return created_response("User accounts linked successfully", {})

            except Exception as e:
                logger.error(f"Identity linking failed: {str(e)}")
                db.rollback()

        # Create CoAuth0User record with basic info from Auth0
        from datetime import datetime
        current_time = datetime.now()

        auth0_user = CoAuth0User(
            userId=user_id,
            email=email,
            name=request.name or "Unknown",
            familyName=request.family_name,
            givenName=request.given_name,
            nickName=request.nickname or "Unknown",
            picture=request.picture or "",
            emailVerified=request.email_verified or False,
            identities=request.identities or [],
            locale=request.locale,
            userMetadata={},
            appMetadata={},
            lastIp="",
            lastLogin=current_time,
            createdAt=current_time,
            updatedAt=current_time,
            loginsCount=request.logins_count or 1
        )

        # Create CoMember record
        member = CoMember(
            auth0Id=user_id,
            loginEmail=email,
            firstName= request.name or None,
            lastName= request.family_name or None,
            loginEmailVerified=request.email_verified or False,
            identityType = request["identities"][0]["connection"] if "identities" in request and request["identities"] else "auth0"
        )

        db.add(auth0_user)
        db.add(member)
        db.commit()
        db.refresh(member)
        db.refresh(auth0_user)
        
        admin_verification = MemberVerification(
            member_uuid=member.uuid,
            verification_status="pending"
        )
        db.add(admin_verification)

        # Set self-referential created/updated fields
        member.createdByMember = member.uuid
        member.updatedByMember = member.uuid
        db.commit()
        db.refresh(member)

        logger.info(f"Created new member: {member.uuid} for Auth0 ID: {user_id}")

        return created_response(
            "Member created successfully",
            {}
        )
        
    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.exception("Database error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Database error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
    except Exception as e:
        logger.exception("Unexpected error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def post_login_trigger(request, db: Session):
    """
    Optimized post-login trigger that:
    1. Checks if user exists in database
    2. If user doesn't exist: calls post-registration to create user
    3. If user creation fails: returns HTTP 500 (causes Auth0 login to fail)
    4. If user exists or is created successfully: returns HTTP 200 immediately
    5. After returning 200: executes Auth0 Management API operations in background
    """
    try:
        email = request.email
        user_id = request.user_id
        identities = request.identities or []

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        existing_member = db.query(CoMember).filter_by(auth0Id=user_id).first()

        if existing_member:
            logger.info(f"User {user_id} already exists in database")
            return success_response("User login processed successfully", {})

        logger.info(f"New user {user_id} detected, processing synchronously")

        existing_member_by_email = db.query(CoAuth0User).filter_by(email=email).first()

        if existing_member_by_email and existing_member_by_email.userId != user_id:
            logger.info(f"Found existing user with email {email}, linking identities synchronously")

            try:
                auth0_identity_service.process_comprehensive_identity_linking(
                    email, user_id, identities, request
                )

                existing_member_by_email.auth0Id = user_id
                db.commit()

                logger.info(f"Identity linking completed for user {user_id}")

            except Exception as e:
                logger.error(f"Identity linking failed: {str(e)}")
                db.rollback()
                post_registration_trigger(request, db)
        else:
            post_registration_trigger(request, db)

        return success_response(
            "Login successful",
            {}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during post-login")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during login",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
