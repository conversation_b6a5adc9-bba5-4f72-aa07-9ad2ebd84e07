from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from models.member_verification import VerificationStatus

class MemberExportFilters(BaseModel):
    """Filters for member export functionality"""
    search: Optional[str] = Field(None, description="General search across multiple fields")
    firstName: Optional[str] = Field(None, description="Filter by first name (partial match)")
    lastName: Optional[str] = Field(None, description="Filter by last name (partial match)")
    email: Optional[str] = Field(None, description="Filter by email (partial match)")
    membershipTier: Optional[str] = Field(None, description="Filter by membership tier")
    communityStatus: Optional[str] = Field(None, description="Filter by community status")
    verificationStatus: Optional[VerificationStatus] = Field(None, description="Filter by verification status")
    organizationName: Optional[str] = Field(None, description="Filter by organization name (partial match)")
    organizationCity: Optional[str] = Field(None, description="Filter by organization city (partial match)")
    organizationState: Optional[str] = Field(None, description="Filter by organization state (partial match)")
    organizationZip: Optional[str] = Field(None, description="Filter by organization ZIP code (partial match)")
    companySize: Optional[str] = Field(None, description="Filter by company size")
    industry: Optional[str] = Field(None, description="Filter by industry (partial match)")
    dateCreatedFrom: Optional[str] = Field(None, description="Filter members created from this date (ISO format)")
    dateCreatedTo: Optional[str] = Field(None, description="Filter members created up to this date (ISO format)")
    
    # Legacy field names for backward compatibility
    city: Optional[str] = Field(None, description="Filter by city (partial match) - legacy field")
    state: Optional[str] = Field(None, description="Filter by state (partial match) - legacy field")
    dateCreated: Optional[str] = Field(None, description="Filter by date created (legacy field)")

    @validator('verificationStatus', pre=True)
    def validate_verification_status(cls, v):
        """Handle empty strings and convert to None for optional verification status"""
        if v == "" or v is None:
            return None
        return v

class MemberExportRequest(BaseModel):
    """Request schema for member export endpoint"""
    filters: Optional[MemberExportFilters] = Field(default_factory=MemberExportFilters, description="Optional filters to apply")
    selectedFields: List[str] = Field(..., description="List of fields to include in export")
    notes: str = Field(..., description="Purpose/reason for the export")

    @validator('selectedFields')
    def validate_selected_fields(cls, v):
        if not v or len(v) == 0:
            raise ValueError('At least one selected field is required')
        
        # Define available fields that can be exported (camelCase)
        available_fields = {
            'firstName', 'lastName', 'loginEmail', 'personalBusinessEmail', 'phone',
            'professionalTitle', 'membershipTier', 'communityStatus', 'verificationStatus', 'dateCreated',
            'organizationName', 'city', 'state', 'zip', 'industry', 'companySize',
            'annualRevenue', 'yearFounded'
        }
        
        # Field name mapping from snake_case to camelCase
        field_mapping = {
            'first_name': 'firstName',
            'last_name': 'lastName',
            'email': 'loginEmail',
            'verification_status': 'verificationStatus',
            'organization_name': 'organizationName',
            'personal_business_email': 'personalBusinessEmail',
            'professional_title': 'professionalTitle',
            'membership_tier': 'membershipTier',
            'community_status': 'communityStatus',
            'date_created': 'dateCreated',
            'company_size': 'companySize',
            'annual_revenue': 'annualRevenue',
            'year_founded': 'yearFounded'
        }
        
        # Convert snake_case to camelCase and validate
        converted_fields = []
        invalid_fields = []
        
        for field in v:
            # Check if it's already in camelCase
            if field in available_fields:
                converted_fields.append(field)
            # Check if it's in snake_case and can be converted
            elif field in field_mapping:
                converted_fields.append(field_mapping[field])
            else:
                invalid_fields.append(field)
        
        if invalid_fields:
            raise ValueError(f'Invalid fields: {", ".join(invalid_fields)}. Available fields: {", ".join(sorted(available_fields))}')
        
        return converted_fields

    @validator('notes')
    def validate_notes(cls, v):
        if not v or v.strip() == '':
            raise ValueError('Notes cannot be empty')
        return v.strip()

    class Config:
        from_attributes = True 