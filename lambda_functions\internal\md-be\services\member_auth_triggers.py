import logging
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from models.member import Co<PERSON><PERSON>ber, CoAuth0User
from utils.response_utils import created_response, success_response
from services.auth0_identity_service import auth0_identity_service
from models.member_verification import MemberVerification

logger = logging.getLogger(__name__)

def post_registration_trigger(request, db: Session):
    try:
        email = request.get("email")
        user_id = request.get("user_id")
        identities = request.get("identities", [])

        # Validate input parameters
        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Check if this exact user_id already exists
        existing_member_by_id = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if existing_member_by_id:
            logger.info(f"User {user_id} already exists in database")
            return created_response(
                "User already exists",
                {}
            )

        # Check if a user with the same email already exists (different user_id)
        existing_member_by_email = db.query(CoAuth0User).filter_by(email=email).first()

        if existing_member_by_email and existing_member_by_email.userId != user_id:
            logger.info(f"Found existing user with email {email}, linking identities synchronously")

            try:
                auth0_identity_service.process_comprehensive_identity_linking(
                    email, user_id, identities, request
                )

                existing_member_by_email.userId = user_id
                db.commit()

                return created_response("User accounts linked successfully", {})

            except Exception as e:
                logger.error(f"Identity linking failed: {str(e)}")
                db.rollback()

        # Create CoAuth0User record with basic info from Auth0
        auth0_user = CoAuth0User(
            userId=user_id,
            email=email,
            name=request.get("name") or None,
            familyName=request.get("family_name") or None,
            givenName=request.get("given_name") or None,
            nickName=request.get("nickname") or None,
            picture=request.get("picture") or None,
            emailVerified=request.get("email_verified") or False,
            identities= request.get("identities") or [],
            locale= request.get("locale") or None,
            userMetadata={},
            appMetadata={},
            lastIp=request.get("last_ip") or None,
            lastLogin=request.get("last_login") or None,
            createdAt= request.get("created_at") or None,
            updatedAt= request.get("updated_at") or None,
            loginsCount=request.get("logins_count") or 0
        )

        # Create CoMember record
        member = CoMember(
            auth0Id=user_id,
            loginEmail=email,
            firstName= request.get("name") or None,
            lastName= request.get("family_name") or None,
            loginEmailVerified=request.get("email_verified") or False,
            identityType=request.get("identities")[0].get("connection") if request.get("identities") else "auth0",
        )

        db.add(auth0_user)
        db.add(member)
        db.commit()
        db.refresh(member)
        db.refresh(auth0_user)
        
        admin_verification = MemberVerification(
            member_uuid=member.uuid,
            verification_status="pending"
        )
        db.add(admin_verification)

        # Set self-referential created/updated fields
        member.createdByMember = member.uuid
        member.updatedByMember = member.uuid
        db.commit()
        db.refresh(member)

        logger.info(f"Created new member: {member.uuid} for Auth0 ID: {user_id}")

        return created_response(
            "Member created successfully",
            {}
        )
        
    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.exception("Database error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Database error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
    except Exception as e:
        logger.exception("Unexpected error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def post_login_trigger(request, db: Session):
    """
    Optimized post-login trigger that:
    1. Checks if user exists in database
    2. If user doesn't exist: calls post-registration to create user
    3. If user creation fails: returns HTTP 500 (causes Auth0 login to fail)
    4. If user exists or is created successfully: returns HTTP 200 immediately
    5. After returning 200: executes Auth0 Management API operations in background
    """
    try:
        # Extract data from request
        email = request.get("email")
        user_id = request.get("user_id")
        identities = request.get("identities", [])

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        existing_member = db.query(CoMember).filter_by(auth0Id=user_id).first()

        if existing_member:
            logger.info(f"User {user_id} already exists in database")
            return success_response("User login processed successfully", {})

        logger.info(f"New user {user_id} detected, processing synchronously")

        existing_member_by_email = db.query(CoMember).filter_by(email=email).first()

        if existing_member_by_email and existing_member_by_email.auth0Id != user_id:
            logger.info(f"Found existing user with email {email}, linking identities synchronously")

            try:
                auth0_identity_service.process_comprehensive_identity_linking(
                    email, user_id, identities, request
                )

                existing_member_by_email.auth0Id = user_id
                db.commit()

                logger.info(f"Identity linking completed for user {user_id}")

            except Exception as e:
                logger.error(f"Identity linking failed: {str(e)}")
                db.rollback()
                post_registration_trigger(request, db)
        else:
            post_registration_trigger(request, db)

        return success_response("User login processed successfully", {})

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during post-login")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during login",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
