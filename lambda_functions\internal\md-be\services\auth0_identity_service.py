import logging
import requests
from typing import Dict, List, Optional, Any
from config import settings
from auth0_manage_api.auth0_manage import get_management_token

logger = logging.getLogger(__name__)

class Auth0IdentityService:
    """
    Service for handling Auth0 Management API operations including
    user search and identity linking functionality.
    """
    
    def __init__(self):
        self.domain = settings.DOMAIN
        self.management_api_audience = settings.AUDIENCE
    
    def get_management_api_token(self) -> str:
        """
        Get Auth0 Management API token using existing utility.
        
        Returns:
            str: Management API access token
            
        Raises:
            Exception: If token retrieval fails
        """
        try:
            return get_management_token()
        except Exception as e:
            logger.error(f"Failed to get Auth0 Management API token: {str(e)}")
            raise
    
    def search_users_by_email(self, email: str, mgmt_token: str) -> List[Dict[str, Any]]:
        """
        Search for users by email using Auth0 Management API.
        
        Args:
            email: Email address to search for
            mgmt_token: Management API access token
            
        Returns:
            List of user objects from Auth0
            
        Raises:
            Exception: If search fails
        """
        try:
            url = f"https://{self.domain}/api/v2/users-by-email"
            headers = {
                "Authorization": f"Bearer {mgmt_token}",
                "Content-Type": "application/json"
            }
            params = {"email": email}
            
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            users = response.json()
            logger.info(f"Found {len(users)} users for email: {email}")
            return users
            
        except requests.RequestException as e:
            logger.error(f"Failed to search users by email {email}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error searching users by email {email}: {str(e)}")
            raise
    
    def link_user_identity(self, primary_user_id: str, secondary_identity: Dict[str, Any], mgmt_token: str) -> bool:
        """
        Link a secondary identity to a primary user account.
        
        Args:
            primary_user_id: The primary user ID to link the identity to
            secondary_identity: The secondary identity to link (contains provider and user_id)
            mgmt_token: Management API access token
            
        Returns:
            bool: True if linking was successful
            
        Raises:
            Exception: If linking fails
        """
        try:
            url = f"https://{self.domain}/api/v2/users/{requests.utils.quote(primary_user_id, safe='')}/identities"
            headers = {
                "Authorization": f"Bearer {mgmt_token}",
                "Content-Type": "application/json"
            }
            
            # Prepare the identity payload
            identity_payload = {
                "provider": secondary_identity.get("provider"),
                "user_id": secondary_identity.get("user_id")
            }
            
            response = requests.post(url, headers=headers, json=identity_payload)
            response.raise_for_status()
            
            logger.info(f"Successfully linked identity {secondary_identity.get('provider')}:{secondary_identity.get('user_id')} to user {primary_user_id}")
            return True
            
        except requests.RequestException as e:
            logger.error(f"Failed to link identity for user {primary_user_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error linking identity for user {primary_user_id}: {str(e)}")
            raise
    
    def process_identity_linking(self, user_email: str, current_user_id: str, current_identities: List[Dict[str, Any]]) -> None:
        """
        Process bidirectional identity linking for a user based on email matching.

        This function handles both scenarios:
        1. Social user logs in first, then email/password user signs up
        2. Email/password user signs up first, then social user logs in

        Strategy:
        - Find the oldest user account (by created_at) as the primary account
        - Link all other identities to the primary account
        - This ensures consistent behavior regardless of signup order

        Args:
            user_email: Email address of the user
            current_user_id: Current user ID from Auth0
            current_identities: List of current user's identities
        """
        try:
            logger.info(f"Starting bidirectional identity linking process for user: {user_email}")

            # Get management API token
            mgmt_token = self.get_management_api_token()

            # Search for users with the same email
            users = self.search_users_by_email(user_email, mgmt_token)

            if len(users) <= 1:
                logger.info(f"No identity linking needed for {user_email} - only one user found")
                return

            logger.info(f"Found {len(users)} users with email {user_email}, checking if linking is needed")

            # Check if the current_user_id is actually an identity on one of the existing users
            # This happens when Auth0 creates a temporary user for social login but the identity
            # gets linked to an existing user
            for user in users:
                user_identities = user.get("identities", [])
                for identity in user_identities:
                    # Check if current_user_id matches this identity
                    identity_full_id = f"{identity.get('provider')}|{identity.get('user_id')}"
                    if identity_full_id == current_user_id or identity.get("user_id") == current_user_id:
                        logger.info(f"Current user_id {current_user_id} is already linked as an identity to user {user['user_id']} - no linking needed")
                        return

            logger.info(f"Identity linking is needed - determining primary account")

            # Find the primary user (oldest account by created_at)
            primary_user = None
            current_user = None

            # Sort users by created_at to find the oldest
            sorted_users = sorted(users, key=lambda u: u.get("created_at", ""))
            primary_user = sorted_users[0] if sorted_users else None

            # Find the current user in the list
            for user in users:
                if user.get("user_id") == current_user_id:
                    current_user = user
                    break

            if not primary_user:
                logger.warning(f"Could not determine primary user for {user_email}")
                return

            if not current_user:
                logger.warning(f"Current user {current_user_id} not found in search results for {user_email}")
                return

            logger.info(f"Primary user determined: {primary_user['user_id']} (created: {primary_user.get('created_at', 'unknown')})")

            # If current user is already the primary user, no linking needed
            if primary_user["user_id"] == current_user_id:
                logger.info(f"Current user {current_user_id} is already the primary user - no linking needed")
                return

            # Get fresh user data to check current identity state
            fresh_users = self.search_users_by_email(user_email, mgmt_token)
            fresh_primary_user = None

            # Find the fresh primary user data
            for user in fresh_users:
                if user["user_id"] == primary_user["user_id"]:
                    fresh_primary_user = user
                    break

            if not fresh_primary_user:
                logger.warning(f"Could not find fresh data for primary user {primary_user['user_id']}")
                return

            # Get current identities on the primary user
            current_primary_identities = fresh_primary_user.get("identities", [])
            logger.info(f"Primary user {fresh_primary_user['user_id']} currently has {len(current_primary_identities)} identities")

            # Link all secondary users' identities to the primary user
            for user in users:
                if user["user_id"] != primary_user["user_id"]:
                    user_identities = user.get("identities", [])
                    logger.info(f"Checking identities from user {user['user_id']} for linking to primary user {primary_user['user_id']}")

                    for identity in user_identities:
                        try:
                            # Extract the identity information
                            identity_provider = identity.get("provider")
                            identity_user_id = identity.get("user_id")

                            # Skip if this identity is already linked to the primary user
                            already_linked = any(
                                pi.get("provider") == identity_provider and
                                pi.get("user_id") == identity_user_id
                                for pi in current_primary_identities
                            )

                            if already_linked:
                                logger.info(f"Identity {identity_provider}:{identity_user_id} already linked to primary user - skipping")
                                continue

                            # Prepare identity to link
                            identity_to_link = {
                                "provider": identity_provider,
                                "user_id": identity_user_id
                            }

                            # Link the identity
                            logger.info(f"Attempting to link {identity_provider}:{identity_user_id} to primary user {primary_user['user_id']}")
                            self.link_user_identity(primary_user["user_id"], identity_to_link, mgmt_token)
                            logger.info(f"Successfully linked {identity_provider}:{identity_user_id} to primary user")

                        except Exception as e:
                            # Check if this is a 409 conflict (identity already exists)
                            if "409" in str(e) or "Conflict" in str(e):
                                logger.info(f"Identity {identity.get('provider')}:{identity.get('user_id')} already linked (409 Conflict) - this is expected")
                            else:
                                logger.error(f"Failed to link identity {identity}: {str(e)}")
                            # Continue with other identities even if one fails

            logger.info(f"Bidirectional identity linking process completed for user: {user_email}")

        except Exception as e:
            logger.error(f"Identity linking process failed for user {user_email}: {str(e)}")
            # Don't re-raise since this is a background operation
            # The main login flow should not be affected

    def process_comprehensive_identity_linking(self, user_email: str, current_user_id: str, current_identities: List[Dict[str, Any]], request_data: Any) -> None:
        """
        Comprehensive identity linking that handles both scenarios:
        1. User signed up with email/password, now logging in with social (link social to email/password user)
        2. User logged in with social first, now signing up with email/password (link email/password to social user)

        This method checks Auth0 directly for existing users and performs the appropriate linking.

        Args:
            user_email: Email address of the user
            current_user_id: Current user ID from Auth0
            current_identities: List of current user's identities
            request_data: Full request data for user creation if needed
        """
        try:
            logger.info(f"Starting comprehensive identity linking for user: {user_email} (user_id: {current_user_id})")

            # Add a small delay to allow Auth0's automatic linking to complete
            # Auth0 often automatically links identities during the signup/login process
            import time
            time.sleep(2)  # 2 second delay
            logger.info("Waited 2 seconds for Auth0 automatic linking to complete")

            # Get management API token
            mgmt_token = self.get_management_api_token()

            # Search for all users with the same email in Auth0
            users = self.search_users_by_email(user_email, mgmt_token)

            if len(users) <= 1:
                logger.info(f"Only one user found for {user_email} - no identity linking needed")
                return

            logger.info(f"Found {len(users)} users with email {user_email} in Auth0")

            # Check if identities are already properly consolidated
            # This happens when Auth0 has already linked the identities to one user
            users_with_multiple_identities = [user for user in users if len(user.get("identities", [])) > 1]

            if users_with_multiple_identities:
                # Identities are already consolidated on one or more users
                logger.info(f"Found {len(users_with_multiple_identities)} users with multiple identities - checking if linking is complete")

                # Check if all expected identities are present on any single user
                all_expected_providers = set()
                for user in users:
                    for identity in user.get("identities", []):
                        all_expected_providers.add(identity.get("provider"))

                for user in users_with_multiple_identities:
                    user_providers = set(identity.get("provider") for identity in user.get("identities", []))
                    if user_providers == all_expected_providers:
                        logger.info(f"All identities already consolidated on user {user['user_id']} - no linking needed")
                        return

            # Find the current user and other users that still need linking
            current_user = None
            other_users = []

            for user in users:
                if user.get("user_id") == current_user_id:
                    current_user = user
                else:
                    # Only consider users that have single identities (not already consolidated)
                    if len(user.get("identities", [])) == 1:
                        other_users.append(user)

            if not current_user:
                logger.warning(f"Current user {current_user_id} not found in Auth0 search results")
                return

            if not other_users:
                logger.info(f"No other users with single identities found for {user_email} - linking may already be complete")
                return

            # Determine the primary user (oldest by created_at)
            all_users = [current_user] + other_users
            sorted_users = sorted(all_users, key=lambda u: u.get("created_at", ""))
            primary_user = sorted_users[0]

            logger.info(f"Primary user determined: {primary_user['user_id']} (created: {primary_user.get('created_at', 'unknown')})")

            # If current user is the primary user, link other users' identities to current user
            if primary_user["user_id"] == current_user_id:
                logger.info(f"Current user {current_user_id} is the primary user - linking other identities to it")

                for other_user in other_users:
                    self._link_user_identities_to_primary(other_user, primary_user, mgmt_token)

            else:
                # Current user is not primary - link current user's identities to primary user
                logger.info(f"Current user {current_user_id} is not primary - linking its identities to primary user {primary_user['user_id']}")

                self._link_user_identities_to_primary(current_user, primary_user, mgmt_token)

            logger.info(f"Comprehensive identity linking completed for user: {user_email}")

        except Exception as e:
            logger.error(f"Comprehensive identity linking failed for user {user_email}: {str(e)}")
            # Don't re-raise since this is a background operation

    def _link_user_identities_to_primary(self, source_user: Dict[str, Any], primary_user: Dict[str, Any], mgmt_token: str) -> None:
        """
        Link all identities from source_user to primary_user.

        Args:
            source_user: User whose identities should be linked to primary
            primary_user: Primary user who should receive the identities
            mgmt_token: Management API token
        """
        try:
            source_identities = source_user.get("identities", [])
            primary_user_id = primary_user["user_id"]
            source_user_id = source_user["user_id"]

            logger.info(f"Checking if identities from {source_user_id} need to be linked to primary user {primary_user_id}")

            # Get fresh primary user data to check current identities
            primary_email = primary_user.get("email")
            if not primary_email:
                logger.warning(f"Primary user {primary_user_id} has no email - cannot refresh data")
                return
            fresh_users = self.search_users_by_email(primary_email, mgmt_token)
            fresh_primary_user = None

            for user in fresh_users:
                if user["user_id"] == primary_user_id:
                    fresh_primary_user = user
                    break

            if not fresh_primary_user:
                logger.warning(f"Could not find fresh data for primary user {primary_user_id}")
                return

            current_primary_identities = fresh_primary_user.get("identities", [])

            # Check if all source identities are already on the primary user
            identities_to_link = []
            for identity in source_identities:
                identity_provider = identity.get("provider")
                identity_user_id = identity.get("user_id")

                already_linked = any(
                    pi.get("provider") == identity_provider and
                    pi.get("user_id") == identity_user_id
                    for pi in current_primary_identities
                )

                if not already_linked:
                    identities_to_link.append(identity)
                else:
                    logger.info(f"Identity {identity_provider}:{identity_user_id} already linked to primary user - skipping")

            if not identities_to_link:
                logger.info(f"All identities from {source_user_id} are already linked to primary user {primary_user_id} - no action needed")
                return

            logger.info(f"Found {len(identities_to_link)} identities that need to be linked from {source_user_id} to primary user {primary_user_id}")

            for identity in source_identities:
                try:
                    identity_provider = identity.get("provider")
                    identity_user_id = identity.get("user_id")

                    # Skip if this identity is already linked to the primary user
                    already_linked = any(
                        pi.get("provider") == identity_provider and
                        pi.get("user_id") == identity_user_id
                        for pi in current_primary_identities
                    )

                    if already_linked:
                        logger.info(f"Identity {identity_provider}:{identity_user_id} already linked to primary user - skipping")
                        continue

                    # Prepare identity to link
                    identity_to_link = {
                        "provider": identity_provider,
                        "user_id": identity_user_id
                    }

                    # Link the identity
                    logger.info(f"Linking {identity_provider}:{identity_user_id} to primary user {primary_user_id}")
                    self.link_user_identity(primary_user_id, identity_to_link, mgmt_token)
                    logger.info(f"Successfully linked {identity_provider}:{identity_user_id} to primary user")

                except Exception as e:
                    # Check if this is a 409 conflict (identity already exists)
                    if "409" in str(e) or "Conflict" in str(e):
                        logger.info(f"Identity {identity.get('provider')}:{identity.get('user_id')} already linked (409 Conflict) - this is expected")
                    else:
                        logger.error(f"Failed to link identity {identity}: {str(e)}")
                    # Continue with other identities even if one fails

        except Exception as e:
            logger.error(f"Failed to link identities from {source_user.get('user_id')} to {primary_user.get('user_id')}: {str(e)}")

# Create a singleton instance for easy import
auth0_identity_service = Auth0IdentityService()
