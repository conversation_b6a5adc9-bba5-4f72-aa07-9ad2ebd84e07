### label details ###
name           = "member-be-api"
environment    = "prod"
project        = "uscc-co"
classification = "confidential"
compliance     = "SOC2"

### External Lambda Details ###
function_name = "us-east1-prod-member-be-api"
package_type  = "Image"
description   = "stg member database be api"
timeout       = 300
memory_size   = 128


### Internal Lambda Details ###
function_name_internal = "us-east1-prod-member-be-api-internal"
description_internal   = "prod internal member database be api"
timeout_internal = 300
membory_size_internal = 128

ecr_repo      = "member-be-api"

## Security Group Variables ###
lambda_sg_tag_name = "SG-PROD-MEMBER-BE-API"

## Kms variables ##
lambda_kms_alias_name = "KMS-PROD-MEMBER-BE-API"
