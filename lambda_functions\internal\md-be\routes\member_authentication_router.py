from fastapi import APIRouter, Depends, status, Request, Query
from sqlalchemy.orm import Session
from controller.member_authentication_controller import MemberAuthenticationController
from controller.member_auth_triggers_controller import MemberAuthTriggersController
from db.db import get_db
from schemas.member import CoMemberCreate, LoginRequest, Provider, CoMemberCreateEnhanced
from pydantic import BaseModel
from dependencies.jwt_verifier import verify_user
from typing import List, Dict, Any, Optional

router = APIRouter()

member_auth_controller = MemberAuthenticationController()
member_auth_triggers_controller = MemberAuthTriggersController()

# Schema for trigger endpoints

class PostLoginRequest(BaseModel):
    email: str
    user_id: str
    name: Optional[str] = None
    last_login: str
    logins_count: Optional[int] = None
    identities: Optional[List[Dict[str, Any]]] = None
    family_name: Optional[str] = None
    given_name: Optional[str] = None
    nickname: Optional[str] = None
    picture: Optional[str] = None
    email_verified: Optional[bool] = None
    locale: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

# authentication Routes
@router.post("/register", status_code=status.HTTP_201_CREATED, include_in_schema=False)
async def create(member: CoMemberCreateEnhanced, db: Session = Depends(get_db)):
    user = {}
    return member_auth_controller.create_member(member, user, db)

@router.post("/login", status_code=status.HTTP_200_OK, include_in_schema=False)
async def login(payload: LoginRequest, db: Session = Depends(get_db)):
    return member_auth_controller.login(payload, db)

# auth0 social login routes
@router.post("/social-login", status_code=status.HTTP_200_OK, include_in_schema=False)
async def login_redirect(provider: Provider):
    return member_auth_controller.login_redirect(provider.provider, provider.redirect_uri)

@router.get("/callback", status_code=status.HTTP_200_OK, include_in_schema=False)
async def callback(
        code: str = Query(..., description="Authorization code from provider"),
        db: Session = Depends(get_db)
    ):
    # code = request.query_params.get("code")
    if code is None:
        return {"error": "Missing 'code' parameter in callback."}

    return member_auth_controller.callback(code, db)

# Auth0 Trigger Endpoints
@router.post("/post-registration", status_code=status.HTTP_201_CREATED)
async def post_registration_trigger(
    request: dict,
    db: Session = Depends(get_db)
):
    """
    Post-registration trigger endpoint
    Called from Auth0 post-registration action with user email and ID
    Creates member in database using provided user details
    """
    return member_auth_triggers_controller.handle_post_registration(request, db)

@router.post("/post-login", status_code=status.HTTP_200_OK)
async def post_login_trigger(
    request: PostLoginRequest,
    db: Session = Depends(get_db)
):
    """
    Post-login trigger endpoint
    Called after user completes login on Auth0 Universal Login
    Exchanges authorization code for tokens, verifies with Auth0 JWKS and returns member info with access token
    """
    return member_auth_triggers_controller.handle_post_login(request, db)

@router.get("/get-member-info")
async def get_member_info(db: Session = Depends(get_db), member_token = Depends(verify_user)):
    """
    Get member information
    """
    return member_auth_controller.get_member_info(member_token, db)