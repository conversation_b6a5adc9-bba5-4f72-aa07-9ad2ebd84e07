import threading
import logging
from typing import Callable, Any, Dict
from functools import wraps

logger = logging.getLogger(__name__)

class BackgroundTaskManager:
    """
    Simple background task manager for executing tasks asynchronously
    after HTTP response is returned to the client.
    
    This is designed for AWS Lambda environments where we want to:
    1. Return HTTP 200 immediately to Auth0
    2. Execute heavy operations (Auth0 Management API calls) in background
    3. Ensure the main response is not blocked by these operations
    """
    
    @staticmethod
    def run_after_response(func: Callable, *args, **kwargs) -> None:
        """
        Execute a function in a background thread after the HTTP response is returned.
        
        Args:
            func: The function to execute in background
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
        """
        def background_task():
            try:
                logger.info(f"Starting background task: {func.__name__}")
                result = func(*args, **kwargs)
                logger.info(f"Background task completed successfully: {func.__name__}")
                return result
            except Exception as e:
                logger.error(f"Background task failed: {func.__name__} - {str(e)}", exc_info=True)
                # Don't re-raise since this is a background task
                # The main response has already been sent
        
        # Start the background thread
        thread = threading.Thread(target=background_task, daemon=True)
        thread.start()
        logger.info(f"Background task started: {func.__name__}")

def run_in_background(func: Callable) -> Callable:
    """
    Decorator to mark a function to be executed in background.
    
    Usage:
        @run_in_background
        def heavy_operation(param1, param2):
            # This will run in background
            pass
            
        # Call it normally, but it will execute in background
        heavy_operation(value1, value2)
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        BackgroundTaskManager.run_after_response(func, *args, **kwargs)
    
    return wrapper

# Convenience function for direct use
def execute_in_background(func: Callable, *args, **kwargs) -> None:
    """
    Convenience function to execute any function in background.
    
    Args:
        func: The function to execute
        *args: Positional arguments for the function
        **kwargs: Keyword arguments for the function
    """
    BackgroundTaskManager.run_after_response(func, *args, **kwargs)
