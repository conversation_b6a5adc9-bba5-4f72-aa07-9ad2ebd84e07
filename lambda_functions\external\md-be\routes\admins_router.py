from fastapi import APIRouter, Depends, status, Query
from fastapi.security import HTTPAuthorizationCredentials, HTTP<PERSON>ear<PERSON>
from sqlalchemy.orm import Session
from controller.admins_controller import AdminController
from controller.database_utils_controller import DatabaseUtilsController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.admin import Admin_cognito_create, AdminUpdate, ForgotPasswordRequest, ConfirmForgotPasswordRequest, ChangePasswordRequest
from schemas.database_utils import SequenceResetRequest
from controller.member_controller import MemberController
from schemas.member import CoMemberCreate, BulkUpsertMembersRequest, BulkDeleteMembersRequest, CoMemberUpdateByAdmin
from schemas.member_export import MemberExportRequest
from jose import jwt
from controller.member_authentication_controller import MemberAuthenticationController
from typing import List, Optional



router = APIRouter()

adminController = AdminController()
member_auth_controller = MemberAuthenticationController()
database_utils_controller = DatabaseUtilsController()
member_controller = MemberController()
memberController = MemberController()


@router.post("/create-member", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_201_CREATED)
async def create_member_by_admin(
    member: CoMemberCreate,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    # return member_auth_controller.create_member(member, admin_user_payload, db)
    return adminController.create_member(member, admin_user_payload, db)

@router.put("/update-member/{uuid}", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_200_OK)
async def update_member_by_admin(
    uuid: str,
    member: CoMemberUpdateByAdmin,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    return adminController.update_member_by_uuid(uuid, member, db, admin_user_payload)

@router.post("/members/export", dependencies=[Depends(validate_jwt_token)])
async def export_members(
    request_data: MemberExportRequest,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """Export members to CSV with optional filters and field selection"""
    return member_controller.export_members(request_data, db, admin_user_payload)

@router.post("/register", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_201_CREATED)
async def create_admin_cognito(
    new_admin: Admin_cognito_create,
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
):
    """Create a new admin with Cognito integration"""
    return adminController.create_admin_cognito(credentials.credentials, new_admin, db)

@router.put("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def update_admin(uuid: str, admin: AdminUpdate, db: Session = Depends(get_db), admin_user_payload=Depends(validate_jwt_token)):
    return adminController.update_admin(uuid, admin, db, admin_user_payload)

@router.get("/admin-list", dependencies=[Depends(validate_jwt_token)])
async def get_admin_list(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Get list of all admins"""
    return adminController.get_admin_list(db, page=page, pageSize=pageSize)


@router.get("/get-admin-user", dependencies=[Depends(validate_jwt_token)])
async def get_admin_user(
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """Get admin user information"""
    return adminController.get_admin_user(admin_user_payload, db)

@router.delete("/{uuid}", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_200_OK)
async def delete_admin(
    uuid: str,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """Delete admin from both Cognito and database"""
    return adminController.delete_admin(uuid, db, admin_user_payload)

@router.get("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_admin_by_uuid(uuid: str, db: Session = Depends(get_db)):
    """Get admin by UUID"""
    return adminController.get_admin_by_uuid(uuid, db)

@router.post("/forgot-password", status_code=status.HTTP_200_OK)
async def initiate_forgot_password(
    request: ForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """Initiate forgot password flow - sends reset code to admin's email"""
    return adminController.initiate_forgot_password(request.username, db)

@router.post("/confirm-forgot-password", status_code=status.HTTP_200_OK)
async def confirm_forgot_password(
    request: ConfirmForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """Confirm forgot password with verification code and set new password"""
    return adminController.confirm_forgot_password(
        request.username, 
        request.confirmationCode, 
        request.newPassword, 
        db
    )

@router.post("/change-password", status_code=status.HTTP_200_OK)
async def change_admin_password(
    request: ChangePasswordRequest,
    db: Session = Depends(get_db)
):
    """Change admin password using access token"""
    return adminController.change_admin_password(request.accessToken, request.newPassword, db)


# Bulk Operations on members (Admin only)
@router.post("/bulk/upsert")
async def bulk_upsert_members(
    request: BulkUpsertMembersRequest,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    return memberController.bulk_upsert_members(request, db, admin_user_payload)

@router.get("/bulk/with-organizations", dependencies=[Depends(validate_jwt_token)])
async def get_members_with_organizations(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    # Sorting parameters
    sortBy: Optional[str] = Query("firstName", description="Sort by field (firstName, lastName, dateCreated, membershipTier, communityStatus, loginEmail)"),
    sortOrder: Optional[str] = Query("asc", description="Sort order (asc, desc)"),
    # Optional filter parameters
    firstName: Optional[str] = Query(None, description="Filter by member first name (partial match)"),
    lastName: Optional[str] = Query(None, description="Filter by member last name (partial match)"),
    email: Optional[str] = Query(None, description="Filter by member email (partial match on login or business email)"),
    membershipTier: Optional[str] = Query(None, description="Filter by membership tier (partial match)"),
    communityStatus: Optional[str] = Query(None, description="Filter by community status (partial match)"),
    verificationStatus: Optional[str] = Query(None, description="Filter by verification status (pending, verified, rejected, under_review)"),
    organizationName: Optional[str] = Query(None, description="Filter by organization name (partial match)"),
    organizationCity: Optional[str] = Query(None, description="Filter by organization city (partial match)"),
    organizationState: Optional[str] = Query(None, description="Filter by organization state (partial match)"),
    organizationZip: Optional[str] = Query(None, description="Filter by organization ZIP code (partial match)"),
    companySize: Optional[str] = Query(None, description="Filter by company size (partial match)"),
    dateCreatedFrom: Optional[str] = Query(None, description="Filter members created from this date (ISO format: YYYY-MM-DDTHH:MM:SSZ)"),
    dateCreatedTo: Optional[str] = Query(None, description="Filter members created up to this date (ISO format: YYYY-MM-DDTHH:MM:SSZ)")
):
    return memberController.get_members_with_organizations(
        db, page, pageSize, sortBy, sortOrder, firstName, lastName, email, membershipTier,
        communityStatus, verificationStatus, organizationName, organizationCity, organizationState,
        organizationZip, companySize, dateCreatedFrom, dateCreatedTo
    )

@router.delete("/bulk/delete")
async def bulk_delete_members(
    request: BulkDeleteMembersRequest,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    return memberController.bulk_delete_members(request, db, admin_user_payload)