from pydantic import BaseModel, Field, computed_field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID

class LogFilters(BaseModel):
    start_timestamp: Optional[datetime] = Field(None, description="Start timestamp for filtering logs")
    end_timestamp: Optional[datetime] = Field(None, description="End timestamp for filtering logs")
    purpose: Optional[str] = Field(None, description="Search text in purpose field")
    action: Optional[str] = Field(None, description="Filter by specific action type")
    action_exclude: Optional[List[str]] = Field(None, description="Exclude logs matching any of these action types")
    sort_by: Optional[str] = Field("timestamp", description="Field to sort by (timestamp, username, action, purpose)")
    sort_order: Optional[str] = Field("desc", description="Sort order: asc or desc")
    limit: Optional[int] = Field(None, ge=1, le=1000, description="Maximum number of records to export (1-1000)")

class LogResponse(BaseModel):
    id: int
    uuid: UUID
    timestamp: datetime
    userUuid: UUID
    username: Optional[str] = Field(None, description="Username of the admin who performed the action")
    admin_uuid: Optional[UUID] = Field(None, description="UUID of the admin who performed the action")
    action: str
    filters_applied: Optional[Dict[str, Any]] = Field(None, description="Filters applied from context JSON")
    selected_fields: Optional[List[str]] = Field(None, description="Selected fields from context JSON")
    purpose: str
    context: Optional[Dict[str, Any]] = Field(None, description="Raw context JSON for the log entry")
    deleted_email: Optional[str] = Field(None, description="Email of the deleted member for member_delete actions")
    deleted_member_uuid: Optional[str] = Field(None, description="UUID of the deleted member for member_delete actions")
    auth0_id: Optional[str] = Field(None, description="Auth0 ID of the deleted member for member_delete actions")

    class Config:
        from_attributes = True

class LogListResponse(BaseModel):
    logs: List[LogResponse]
    total_count: int
    page: int
    page_size: int

class LogExportRequest(BaseModel):
    filters: LogFilters
    selected_fields: List[str] = Field(..., description="Fields to include in CSV export")
    notes: Optional[str] = Field(None, description="Purpose of this export action")
    limit: Optional[int] = Field(None, ge=1, le=1000, description="Maximum number of records to export (1-1000, overrides filters.limit)")
    
    @computed_field
    @property
    def effective_limit(self) -> int:
        """Get the effective limit, prioritizing top-level limit over filters.limit"""
        if self.limit is not None:
            return self.limit
        return self.filters.limit or 1000

class LogExportResponse(BaseModel):
    csv_content: str
    filename: str
    export_timestamp: datetime 