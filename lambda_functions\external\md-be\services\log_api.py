from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text, String, desc, asc
from models.log import CoLog
from models.admin import AdminModel
from schemas.log import LogFilters, LogExportRequest, LogResponse
from typing import List, Dict, Any, Optional, Tuple
import csv
import io
from datetime import datetime
from uuid import UUID


def build_log_query(db: Session, filters: LogFilters):
    """
    Build a query that applies filters to the log table
    """
    # Extract JSON fields from context as separate columns and join with admin table
    base_query = db.query(
        CoLog.id,
        CoLog.uuid,
        CoLog.timestamp,
        CoLog.userUuid,
        CoLog.action,
        CoLog.context.label('context'),
        CoLog.context['filters_applied'].label('filters_applied'),
        CoLog.context['selected_fields'].label('selected_fields'),
        CoLog.context['deleted_member_uuid'].label('ctx_deleted_member_uuid'),
        CoLog.context['deleted_email'].label('ctx_deleted_email'),
        CoLog.context['auth0_id'].label('ctx_auth0_id'),
        CoLog.purpose,
        AdminModel.username,
        AdminModel.uuid.label('admin_uuid')
    ).outerjoin(AdminModel, CoLog.userUuid.cast(String) == AdminModel.cognitoId)
    
    filter_conditions = []
    
    # Time range filters
    if filters.start_timestamp:
        filter_conditions.append(CoLog.timestamp >= filters.start_timestamp)
    
    if filters.end_timestamp:
        filter_conditions.append(CoLog.timestamp <= filters.end_timestamp)
    
    # Purpose text search (case-insensitive)
    if filters.purpose:
        filter_conditions.append(CoLog.purpose.ilike(f"%{filters.purpose}%"))
    
    # Action filter (exact match)
    if filters.action:
        filter_conditions.append(CoLog.action == filters.action)
    
    # Action exclusion filter (exclude any logs with actions in the list)
    if getattr(filters, 'action_exclude', None):
        filter_conditions.append(~CoLog.action.in_(filters.action_exclude))
    
    # Apply all filters
    if filter_conditions:
        base_query = base_query.filter(and_(*filter_conditions))
    
    return base_query


def apply_sorting(query, sort_by: str, sort_order: str):
    """
    Apply sorting to the query based on sort_by and sort_order parameters
    """
    
    # Define valid sort fields and their corresponding database columns
    valid_sort_fields = {
        'timestamp': CoLog.timestamp,
        'username': AdminModel.username,
        'action': CoLog.action,
        'purpose': CoLog.purpose
    }
    
    # Validate sort_by field
    if sort_by not in valid_sort_fields:
        # Default to timestamp if invalid field provided
        sort_by = 'timestamp'
    
    # Validate sort_order
    if sort_order.lower() not in ['asc', 'desc']:
        # Default to desc if invalid order provided
        sort_order = 'desc'
    
    # Apply sorting
    sort_column = valid_sort_fields[sort_by]
    if sort_order.lower() == 'desc':
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    return query


def get_logs_with_pagination(
    db: Session, 
    filters: LogFilters, 
    page: int = 1, 
    page_size: int = 50
) -> Tuple[List[LogResponse], int]:
    """
    Get logs with pagination and filtering
    """
    # Build base query
    query = build_log_query(db, filters)
    
    # Get total count
    total_count = query.count()
    
    # Apply sorting
    query = apply_sorting(query, filters.sort_by, filters.sort_order)
    
    # Apply pagination
    offset = (page - 1) * page_size
    logs = query.offset(offset).limit(page_size).all()
    
    # Convert to response models
    log_responses = []
    for log in logs:
        log_response = LogResponse(
            id=log.id,
            uuid=log.uuid,
            timestamp=log.timestamp,
            userUuid=log.userUuid,
            username=log.username,
            admin_uuid=log.admin_uuid,
            action=log.action,
            filters_applied=log.filters_applied,
            selected_fields=log.selected_fields,
            purpose=log.purpose,
            context=log.context,
            deleted_email=log.ctx_deleted_email,
            deleted_member_uuid=log.ctx_deleted_member_uuid,
            auth0_id=log.ctx_auth0_id
        )
        log_responses.append(log_response)
    
    return log_responses, total_count


def format_field_value_for_csv(value: Any) -> str:
    """
    Format a field value for CSV output
    """
    if value is None:
        return ''
    elif isinstance(value, datetime):
        return value.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(value, UUID):
        return str(value)
    elif isinstance(value, (dict, list)):
        import json
        try:
            json_str = json.dumps(value, separators=(',', ':'), default=str)
            json_str = json_str.replace('\n', ' ').replace('\r', ' ').strip()
            return json_str
        except:
            return str(value)
    else:
        return str(value)


def get_field_value_from_log(log_data: Any, field: str) -> Any:
    """
    Get the value for a field from log data
    """
    if field == 'timestamp':
        return log_data.timestamp
    elif field == 'username':
        return log_data.username
    elif field == 'admin_uuid':
        return log_data.admin_uuid
    elif field == 'action':
        return log_data.action
    elif field == 'filters_applied':
        return log_data.filters_applied
    elif field == 'selected_fields':
        return log_data.selected_fields
    elif field == 'purpose':
        return log_data.purpose
    elif field == 'deleted_member_uuid':
        return getattr(log_data, 'deleted_member_uuid', None)
    elif field == 'deleted_email':
        # Try to get from the mapped field first
        email = getattr(log_data, 'deleted_email', None)
        if email is None and hasattr(log_data, 'context') and log_data.context:
            # Fallback: extract from context JSON
            try:
                email = log_data.context.get('deleted_email')
            except:
                pass
        return email
    elif field == 'auth0_id':
        return getattr(log_data, 'auth0_id', None)
    else:
        return None


def generate_csv_from_logs(logs: List[Any], selected_fields: List[str]) -> str:
    """
    Generate CSV content from logs with selected fields
    """
    if not logs:
        return ""
    
    csv_output = io.StringIO()
    
    field_mapping = {
        'timestamp': 'Timestamp',
        'username': 'Username',
        'admin_uuid': 'Admin UUID',
        'action': 'Action',
        'filters_applied': 'Filters Applied',
        'selected_fields': 'Selected Fields',
        'purpose': 'Purpose',
        'deleted_member_uuid': 'Deleted Member UUID',
        'deleted_email': 'Deleted Email',
        'auth0_id': 'Auth0 ID'
    }
    
    # Filter fields based on action type
    filtered_fields = selected_fields.copy()
    if logs and any(log.action == 'member_delete' for log in logs):
        # For member_delete: exclude context fields, include deletion-specific fields
        filtered_fields = [field for field in selected_fields if field not in ['filters_applied', 'selected_fields']]
    else:
        # For other actions: exclude deletion-specific fields
        filtered_fields = [field for field in selected_fields if field not in ['deleted_email', 'deleted_member_uuid', 'auth0_id']]
    
    headers = [field_mapping.get(field, field) for field in filtered_fields]
    
    writer = csv.writer(csv_output, quoting=csv.QUOTE_MINIMAL)
    writer.writerow(headers)
    
    for log in logs:
        row = []
        for field in filtered_fields:
            value = get_field_value_from_log(log, field)
            row.append(format_field_value_for_csv(value))
        writer.writerow(row)
    
    csv_content = csv_output.getvalue()
    csv_output.close()
    
    return csv_content

def log_export_action(
    db: Session, 
    user_uuid: UUID, 
    request_data: LogExportRequest, 
    filters_applied: Dict[str, Any]
) -> None:
    """
    Log the export action to the universal log table
    """
    try:
        context_data = {
            "selected_fields": request_data.selected_fields,
            "filters_applied": filters_applied,
            "notes": request_data.notes,
            "limit": request_data.limit
        }
        
        log_entry = CoLog(
            userUuid=user_uuid,
            action="log_export",
            context=context_data,
            purpose=request_data.notes or "Log export"
        )
        
        db.add(log_entry)
        db.commit()
        
    except Exception as e:
        # Log the error but don't fail the export
        print(f"Warning: Failed to log export action: {str(e)}")
        db.rollback()

def export_logs(
    db: Session,
    request_data: LogExportRequest,
    user_uuid: UUID
) -> str:
    """
    Main export function that handles the complete log export process
    """
    # Get logs that match the filters with limit (instead of all records)
    query = build_log_query(db, request_data.filters)
    
    # Apply sorting for export
    query = apply_sorting(query, request_data.filters.sort_by, request_data.filters.sort_order)
    
    # Apply limit to the query using effective_limit
    logs = query.limit(request_data.effective_limit).all()
    
    if not logs:
        # No logs found - return empty CSV with headers
        csv_output = io.StringIO()
        writer = csv.writer(csv_output, quoting=csv.QUOTE_MINIMAL)
        
        # Create headers - filter fields based on action type
        field_mapping = {
            'timestamp': 'Timestamp',
            'username': 'Username',
            'admin_uuid': 'Admin UUID',
            'action': 'Action',
            'filters_applied': 'Filters Applied',
            'selected_fields': 'Selected Fields',
            'purpose': 'Purpose',
            'deleted_member_uuid': 'Deleted Member UUID',
            'deleted_email': 'Deleted Email',
            'auth0_id': 'Auth0 ID'
        }
        
        # Filter fields based on action type
        filtered_fields = request_data.selected_fields.copy()
        if request_data.filters.action == 'member_delete':
            # For member_delete: exclude context fields, include deletion-specific fields
            filtered_fields = [field for field in request_data.selected_fields if field not in ['filters_applied', 'selected_fields']]
        else:
            # For other actions: exclude deletion-specific fields
            filtered_fields = [field for field in request_data.selected_fields if field not in ['deleted_email', 'deleted_member_uuid', 'auth0_id']]
        
        headers = [field_mapping.get(field, field) for field in filtered_fields]
        writer.writerow(headers)
        
        csv_content = csv_output.getvalue()
        csv_output.close()
        
        # Log the export action
        log_export_action(db, user_uuid, request_data, {})
        return csv_content
    
    # Generate CSV content
    csv_content = generate_csv_from_logs(logs, request_data.selected_fields)
    
    # Prepare filters for logging (only include non-None values)
    filters_applied = {}
    if request_data.filters:
        for field, value in request_data.filters.model_dump().items():
            if value is not None:
                if isinstance(value, datetime):
                    filters_applied[field] = value.isoformat()
                else:
                    filters_applied[field] = value
    
    # Add effective limit to filters_applied for logging
    filters_applied['limit'] = request_data.effective_limit
    
    # Log the export action
    log_export_action(db, user_uuid, request_data, filters_applied)
    
    return csv_content

def get_available_actions(db: Session) -> List[str]:
    """
    Get list of unique actions available in the log table
    """
    actions = db.query(CoLog.action).distinct().all()
    return [action[0] for action in actions if action[0]] 