### label details ###
name           = "member-be-api"
environment    = "prod"
project        = "uscc-co"
classification = "confidential"
compliance     = "SOC2"

### Lambda Details ###
function_name = "us-east1-prod-member-be-api-internal"
package_type  = "Image"
description   = "prod member database be api"
timeout       = 300
memory_size   = 128

ecr_repo      = "member-be-api"

## Security Group Variables ###
lambda_sg_tag_name = "SG-PROD-MEMBER-BE-API-INTERNAL"

## Kms variables ##
lambda_kms_alias_name = "KMS-PROD-MEMBER-BE-API-INTERNAL"
