from attr import validate
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.member_controller import MemberController
from db.db import get_db
from dependencies.jwt_verifier import verify_user
from dependencies.admin_jwt import validate_jwt_token
from schemas.member import CoMemberCreate, CoMemberUpdate, BulkUpsertMembersRequest, BulkDeleteMembersRequest
from typing import List, Optional
from schemas.organization import OrganizationCreate, OrganizationUpdate
from controller.organization_controller import OrganizationController

router = APIRouter()

memberController = MemberController()
organizationController = OrganizationController()

# GET Routes
@router.get("/total-member-count", dependencies=[Depends(verify_user)])
async def get_total_member_count(db: Session = Depends(get_db)):
    return memberController.get_total_member_count(db)

@router.get("/", dependencies=[Depends(verify_user)])
async def get_all_members(
    db: Session = Depends(get_db), 
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    identityType: Optional[str] = Query(None, description="Filter by identity type")
):
    return memberController.get_all_members(db, page=page, pageSize=pageSize, identityType=identityType)

@router.get("/uuid/{uuid}", dependencies=[Depends(verify_user)])
async def get_member_by_uuid(uuid: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_uuid(uuid, db)

@router.get("/auth0/{auth0id}", dependencies=[Depends(verify_user)])
async def get_member_by_auth0id(auth0id: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_auth0id(auth0id, db)

@router.get("/loginemail/{email}", dependencies=[Depends(verify_user)])
async def get_member_by_email(email: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_email(email, db)

@router.put("/uuid/{uuid}")
async def update_member_by_uuid(uuid: str, member: CoMemberUpdate, db: Session = Depends(get_db), admin_user_payload=Depends(verify_user)):
    return memberController.update_member_by_uuid(uuid, member, db, admin_user_payload)

@router.delete("/{uuid}")
async def delete_member(uuid: str, db: Session = Depends(get_db), admin_user_payload=Depends(verify_user)):
    return memberController.delete_member(uuid, db, admin_user_payload)

@router.post("/organization")
async def create_organization_by_member(
    organization: OrganizationCreate,
    user: dict = Depends(verify_user),
    db: Session = Depends(get_db)
):
    return organizationController.create_organization_by_member(organization, user, db)

@router.put("/organization/{org_uuid}")
async def update_organization_by_member(
    org_uuid: str,
    organization: OrganizationUpdate,
    user: dict = Depends(verify_user),
    db: Session = Depends(get_db)
):
    return organizationController.update_organization_by_member(org_uuid, organization, user, db)