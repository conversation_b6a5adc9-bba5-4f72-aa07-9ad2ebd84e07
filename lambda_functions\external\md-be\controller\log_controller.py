from sqlalchemy.orm import Session
from services.log_api import (
    get_logs_with_pagination,
    export_logs,
    get_available_actions
)
from schemas.log import (
    LogFilters,
    LogListResponse,
    LogExportRequest,
    LogExportResponse
)
from utils.response_utils import (
    success_response, bad_request_response, internal_server_error_response
)
from datetime import datetime
from uuid import UUID
import base64


def list_logs(
    filters: LogFilters,
    page: int,
    page_size: int,
    db: Session
):
    """
    Handle log listing with pagination and filtering
    """
    try:
        # Validate pagination parameters
        if page < 1:
                    return bad_request_response("Page number must be greater than 0")
        
        if page_size < 1 or page_size > 1000:
                    return bad_request_response("Page size must be between 1 and 1000")
        
        # Validate date range if both timestamps are provided
        if filters.start_timestamp and filters.end_timestamp:
            if filters.start_timestamp > filters.end_timestamp:
                return bad_request_response("Start timestamp cannot be after end timestamp")
        
        # Validate sort parameters
        valid_sort_fields = ['timestamp', 'username', 'action', 'purpose']
        if filters.sort_by and filters.sort_by not in valid_sort_fields:
            return bad_request_response(f"Invalid sort_by field. Must be one of: {', '.join(valid_sort_fields)}")
        
        if filters.sort_order and filters.sort_order.lower() not in ['asc', 'desc']:
            return bad_request_response("Invalid sort_order. Must be 'asc' or 'desc'")
        
        # Get logs with pagination
        logs, total_count = get_logs_with_pagination(db, filters, page, page_size)
        
        response_data = LogListResponse(
            logs=logs,
            total_count=total_count,
            page=page,
            page_size=page_size
        )
        
        return success_response("Logs retrieved successfully", response_data.model_dump())
        
    except Exception as e:
        return internal_server_error_response(f"Failed to retrieve logs: {str(e)}")


def export_logs_to_csv(
    request_data: LogExportRequest,
    user_uuid: UUID,
    db: Session
):
    """
    Handle log export to CSV
    """
    try:
        # Validate selected fields
        valid_fields = [
            'timestamp', 'username', 'admin_uuid', 'action', 'filters_applied', 
            'selected_fields', 'purpose', 'deleted_member_uuid', 'deleted_email', 'auth0_id'
        ]
        
        invalid_fields = [field for field in request_data.selected_fields if field not in valid_fields]
        if invalid_fields:
            return bad_request_response(f"Invalid fields selected: {', '.join(invalid_fields)}")
        
        # Validate limit parameters (both top-level and filters.limit)
        if request_data.limit is not None and (request_data.limit < 1 or request_data.limit > 1000):
            return bad_request_response("Top-level limit must be between 1 and 1000")
        
        if request_data.filters.limit is not None and (request_data.filters.limit < 1 or request_data.filters.limit > 1000):
            return bad_request_response("Filters limit must be between 1 and 1000")
        
        # Validate date range if both timestamps are provided
        if request_data.filters.start_timestamp and request_data.filters.end_timestamp:
            if request_data.filters.start_timestamp > request_data.filters.end_timestamp:
                return bad_request_response("Start timestamp cannot be after end timestamp")
        
        # Validate sort parameters
        valid_sort_fields = ['timestamp', 'username', 'action', 'purpose']
        if request_data.filters.sort_by and request_data.filters.sort_by not in valid_sort_fields:
            return bad_request_response(f"Invalid sort_by field. Must be one of: {', '.join(valid_sort_fields)}")
        
        if request_data.filters.sort_order and request_data.filters.sort_order.lower() not in ['asc', 'desc']:
            return bad_request_response("Invalid sort_order. Must be 'asc' or 'desc'")
        
        # Generate CSV content
        csv_content = export_logs(db, request_data, user_uuid)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs_export_{timestamp}.csv"
        
        # Encode CSV content to base64 for safe transport
        csv_base64 = base64.b64encode(csv_content.encode('utf-8')).decode('utf-8')
        
        response_data = LogExportResponse(
            csv_content=csv_base64,
            filename=filename,
            export_timestamp=datetime.now()
        )
        
        return success_response(response_data)
        
    except Exception as e:
        return internal_server_error_response(f"Failed to export logs: {str(e)}")


def get_log_actions(db: Session):
    """
    Get available action types for filtering
    """
    try:
        actions = get_available_actions(db)
        
        return success_response("Available actions retrieved successfully", {"actions": actions})
        
    except Exception as e:
        return internal_server_error_response(f"Failed to retrieve actions: {str(e)}")


def get_log_export_fields():
    """
    Get available fields for export
    """
    try:
        fields = [
            {
                "field": "timestamp",
                "display_name": "Timestamp",
                "description": "When the log entry was created"
            },
            {
                "field": "username",
                "display_name": "Username",
                "description": "Username of the admin who performed the action"
            },
            {
                "field": "admin_uuid",
                "display_name": "Admin UUID",
                "description": "UUID of the admin who performed the action"
            },
            {
                "field": "action",
                "display_name": "Action",
                "description": "Type of action performed"
            },
            {
                "field": "filters_applied",
                "display_name": "Filters Applied",
                "description": "Filters applied from context JSON"
            },
            {
                "field": "selected_fields",
                "display_name": "Selected Fields",
                "description": "Selected fields from context JSON"
            },
            {
                "field": "purpose",
                "display_name": "Purpose",
                "description": "Purpose or description of the action"
            },
            {
                "field": "deleted_member_uuid",
                "display_name": "Deleted Member UUID",
                "description": "UUID of the deleted member (only for member_delete)"
            },
            {
                "field": "deleted_email",
                "display_name": "Deleted Email",
                "description": "Email of the deleted member (only for member_delete)"
            },
            {
                "field": "auth0_id",
                "display_name": "Auth0 ID",
                "description": "Auth0 identifier of the deleted member (only for member_delete)"
            }
        ]
        
        return success_response("Export fields retrieved successfully", {"fields": fields})
        
    except Exception as e:
        return internal_server_error_response(f"Failed to retrieve export fields: {str(e)}") 